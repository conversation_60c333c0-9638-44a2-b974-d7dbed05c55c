import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';

const TypewriterText = ({
  text,
  speed = 50,
  className = '',
  onComplete = () => {},
  startDelay = 0,
  showCursor = true
}) => {
  const [displayedText, setDisplayedText] = useState('');
  const [isComplete, setIsComplete] = useState(false);

  useEffect(() => {
    // Сброс состояния при изменении текста
    setDisplayedText('');
    setIsComplete(false);

    if (!text) {
      console.log('TypewriterText: No text provided');
      return;
    }

    console.log('TypewriterText: Starting animation for text:', text.substring(0, 50) + '...');

    // Задержка перед началом печатания
    const startTimer = setTimeout(() => {
      let index = 0;

      const typeNextCharacter = () => {
        if (index < text.length) {
          index++;
          setDisplayedText(text.slice(0, index));

          // Определяем задержку в зависимости от символа
          let delay = speed;
          const currentChar = text[index - 1];

          // Более длинные паузы после знаков препинания
          if (currentChar === '.' || currentChar === '!' || currentChar === '?') {
            delay = speed * 4;
          } else if (currentChar === ',' || currentChar === ';' || currentChar === ':') {
            delay = speed * 2;
          } else if (currentChar === ' ') {
            delay = speed * 1.2;
          }

          setTimeout(typeNextCharacter, delay);
        } else {
          setIsComplete(true);
          onComplete();
          console.log('TypewriterText: Animation completed');
        }
      };

      typeNextCharacter();
    }, startDelay);

    return () => {
      clearTimeout(startTimer);
    };
  }, [text, speed, onComplete, startDelay]);

  return (
    <span className={className}>
      {displayedText}
      {showCursor && !isComplete && (
        <motion.span
          className="inline-block w-0.5 h-4 sm:h-5 lg:h-6 bg-current ml-0.5 rounded-full"
          animate={{ opacity: [1, 0] }}
          transition={{
            duration: 0.6,
            repeat: Infinity,
            repeatType: "reverse",
            ease: "easeInOut"
          }}
        />
      )}
      {showCursor && isComplete && (
        <motion.span
          className="inline-block w-0.5 h-4 sm:h-5 lg:h-6 bg-current ml-0.5 rounded-full"
          initial={{ opacity: 1 }}
          animate={{ opacity: 0 }}
          transition={{ delay: 2, duration: 0.5 }}
        />
      )}
    </span>
  );
};

export default TypewriterText;
