import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';

const TypewriterText = ({
  text,
  speed = 50,
  className = '',
  onComplete = () => {},
  startDelay = 0,
  showCursor = true
}) => {
  const [displayedText, setDisplayedText] = useState('');
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isComplete, setIsComplete] = useState(false);

  useEffect(() => {
    // Сброс состояния при изменении текста
    setDisplayedText('');
    setCurrentIndex(0);
    setIsComplete(false);

    // Задержка перед началом печатания
    const startTimer = setTimeout(() => {
      let timeoutId;

      const typeNextCharacter = (index) => {
        if (index <= text.length) {
          setDisplayedText(text.slice(0, index));
          setCurrentIndex(index);

          if (index === text.length) {
            setIsComplete(true);
            onComplete();
            return;
          }

          // Определяем задержку в зависимости от символа
          let delay = speed;
          const currentChar = text[index - 1];
          const nextChar = text[index];

          // Более длинные паузы после знаков препинания
          if (currentChar === '.' || currentChar === '!' || currentChar === '?') {
            delay = speed * 8; // Длинная пауза после предложения
          } else if (currentChar === ',' || currentChar === ';' || currentChar === ':') {
            delay = speed * 4; // Средняя пауза после запятой
          } else if (currentChar === ' ') {
            delay = speed * 1.5; // Небольшая пауза после пробела
          } else if (nextChar === ' ') {
            delay = speed * 0.8; // Чуть быстрее перед пробелом
          }

          timeoutId = setTimeout(() => typeNextCharacter(index + 1), delay);
        }
      };

      typeNextCharacter(1);

      return () => {
        if (timeoutId) clearTimeout(timeoutId);
      };
    }, startDelay);

    return () => clearTimeout(startTimer);
  }, [text, speed, onComplete, startDelay]);

  return (
    <span className={className}>
      {displayedText}
      {showCursor && !isComplete && (
        <motion.span
          className="inline-block w-0.5 h-4 sm:h-5 lg:h-6 bg-current ml-0.5 rounded-full"
          animate={{ opacity: [1, 0] }}
          transition={{
            duration: 0.6,
            repeat: Infinity,
            repeatType: "reverse",
            ease: "easeInOut"
          }}
        />
      )}
      {showCursor && isComplete && (
        <motion.span
          className="inline-block w-0.5 h-4 sm:h-5 lg:h-6 bg-current ml-0.5 rounded-full"
          initial={{ opacity: 1 }}
          animate={{ opacity: 0 }}
          transition={{ delay: 2, duration: 0.5 }}
        />
      )}
    </span>
  );
};

export default TypewriterText;
